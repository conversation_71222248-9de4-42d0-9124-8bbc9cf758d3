// Simple script to check what columns exist in the profiles table
const { createClient } = require("@supabase/supabase-js");
require("dotenv").config({ path: ".env.local" });

async function checkDatabaseColumns() {
  console.log("🔍 Checking database columns...\n");

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !serviceKey) {
    console.log("❌ Missing environment variables");
    return;
  }

  const supabase = createClient(supabaseUrl, serviceKey);

  try {
    // Try to select all columns from profiles table
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .limit(1);

    if (error) {
      console.log("❌ Error accessing profiles table:", error.message);
      return;
    }

    if (data && data.length > 0) {
      console.log("✅ Successfully accessed profiles table");
      console.log("📋 Current columns:", Object.keys(data[0]));
      
      const requiredColumns = ['id', 'username', 'full_name', 'avatar_url', 'images_generated', 'paid', 'subscription_id'];
      const existingColumns = Object.keys(data[0]);
      const missingColumns = requiredColumns.filter(col => !existingColumns.includes(col));
      
      if (missingColumns.length === 0) {
        console.log("✅ All required columns are present!");
      } else {
        console.log("❌ Missing columns:", missingColumns);
        console.log("\n🔧 To fix this, run the following SQL in Supabase Dashboard:");
        console.log("   Go to: https://supabase.com/dashboard → Your Project → SQL Editor");
        console.log("   Then run:");
        console.log("");
        missingColumns.forEach(col => {
          switch(col) {
            case 'images_generated':
              console.log("   ALTER TABLE profiles ADD COLUMN images_generated INTEGER DEFAULT 0;");
              break;
            case 'paid':
              console.log("   ALTER TABLE profiles ADD COLUMN paid BOOLEAN DEFAULT false;");
              break;
            case 'subscription_id':
              console.log("   ALTER TABLE profiles ADD COLUMN subscription_id TEXT DEFAULT '';");
              break;
          }
        });
      }
    } else {
      console.log("📋 No records found in profiles table (this is normal for new projects)");
      console.log("🔧 Please run the SQL migration in Supabase Dashboard to add missing columns");
    }

  } catch (error) {
    console.log("❌ Error:", error.message);
  }
}

checkDatabaseColumns();
