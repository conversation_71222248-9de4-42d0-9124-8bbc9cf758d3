"use client";

import { useState, useCallback, useRef } from "react";
import {
  generatePerformanceRecommendations,
  generatePerformanceInsights,
  calculatePerformanceScore,
  PerformanceInsight,
} from "@/lib/performanceRecommendations";

export interface ImageMetrics {
  fileSize: number;
  fileSizeFormatted: string;
  dimensions: { width: number; height: number };
  format: string;
  uploadTime: number;
  backgroundRemovalTime: number;
  renderTime: number;
  totalProcessingTime: number;
  memoryUsage: {
    before: number;
    after: number;
    peak: number;
  };
  networkLatency?: number;
  compressionRatio?: number;
  steps: ProcessingStep[];
}

export interface ProcessingStep {
  id: string;
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  status: "pending" | "in-progress" | "completed" | "error";
  details?: any;
  memorySnapshot?: number;
}

export interface PerformanceRecommendation {
  type: "warning" | "error" | "info" | "success";
  title: string;
  description: string;
  impact: "low" | "medium" | "high";
  solution: string;
}

export const useImagePerformance = () => {
  const [metrics, setMetrics] = useState<ImageMetrics | null>(null);
  const [isTracking, setIsTracking] = useState(false);
  const [recommendations, setRecommendations] = useState<
    PerformanceRecommendation[]
  >([]);
  const [insights, setInsights] = useState<PerformanceInsight[]>([]);
  const [performanceScore, setPerformanceScore] = useState<number>(0);

  const startTimeRef = useRef<number>(0);
  const stepsRef = useRef<ProcessingStep[]>([]);
  const memoryRef = useRef<{ before: number; peak: number }>({
    before: 0,
    peak: 0,
  });

  // Utility function to get memory usage
  const getMemoryUsage = useCallback(() => {
    if ("memory" in performance) {
      return (performance as any).memory.usedJSHeapSize / 1024 / 1024; // MB
    }
    return 0;
  }, []);

  // Format file size
  const formatFileSize = useCallback((bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }, []);

  // Get image dimensions and format
  const getImageInfo = useCallback(
    (
      file: File
    ): Promise<{ width: number; height: number; format: string }> => {
      return new Promise((resolve) => {
        const img = new Image();
        img.onload = () => {
          resolve({
            width: img.naturalWidth,
            height: img.naturalHeight,
            format: file.type.split("/")[1].toUpperCase(),
          });
          URL.revokeObjectURL(img.src);
        };
        img.src = URL.createObjectURL(file);
      });
    },
    []
  );

  // Start tracking performance
  const startTracking = useCallback(
    (file: File) => {
      setIsTracking(true);
      startTimeRef.current = performance.now();
      memoryRef.current.before = getMemoryUsage();
      memoryRef.current.peak = memoryRef.current.before;
      stepsRef.current = [];

      // Initialize metrics
      getImageInfo(file).then((info) => {
        const initialMetrics: ImageMetrics = {
          fileSize: file.size,
          fileSizeFormatted: formatFileSize(file.size),
          dimensions: { width: info.width, height: info.height },
          format: info.format,
          uploadTime: 0,
          backgroundRemovalTime: 0,
          renderTime: 0,
          totalProcessingTime: 0,
          memoryUsage: {
            before: memoryRef.current.before,
            after: 0,
            peak: memoryRef.current.before,
          },
          steps: [],
        };
        setMetrics(initialMetrics);
      });
    },
    [getMemoryUsage, formatFileSize, getImageInfo]
  );

  // Add a processing step
  const addStep = useCallback(
    (name: string, details?: any) => {
      const step: ProcessingStep = {
        id: `step-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        name,
        startTime: performance.now(),
        status: "in-progress",
        details,
        memorySnapshot: getMemoryUsage(),
      };

      stepsRef.current.push(step);

      // Update peak memory usage
      const currentMemory = getMemoryUsage();
      if (currentMemory > memoryRef.current.peak) {
        memoryRef.current.peak = currentMemory;
      }

      setMetrics((prev) =>
        prev
          ? {
              ...prev,
              steps: [...stepsRef.current],
            }
          : null
      );

      return step.id;
    },
    [getMemoryUsage]
  );

  // Complete a processing step
  const completeStep = useCallback((stepId: string, details?: any) => {
    const stepIndex = stepsRef.current.findIndex((step) => step.id === stepId);
    if (stepIndex !== -1) {
      const step = stepsRef.current[stepIndex];
      step.endTime = performance.now();
      step.duration = step.endTime - step.startTime;
      step.status = "completed";
      if (details) step.details = { ...step.details, ...details };

      setMetrics((prev) =>
        prev
          ? {
              ...prev,
              steps: [...stepsRef.current],
            }
          : null
      );
    }
  }, []);

  // Mark step as error
  const errorStep = useCallback((stepId: string, error: any) => {
    const stepIndex = stepsRef.current.findIndex((step) => step.id === stepId);
    if (stepIndex !== -1) {
      const step = stepsRef.current[stepIndex];
      step.endTime = performance.now();
      step.duration = step.endTime - step.startTime;
      step.status = "error";
      step.details = { ...step.details, error: error.message || error };

      setMetrics((prev) =>
        prev
          ? {
              ...prev,
              steps: [...stepsRef.current],
            }
          : null
      );
    }
  }, []);

  // Finish tracking and calculate final metrics
  const finishTracking = useCallback(() => {
    if (!metrics) return;

    const totalTime = performance.now() - startTimeRef.current;
    const finalMemory = getMemoryUsage();

    // Calculate individual step times
    const uploadTime =
      stepsRef.current.find((s) => s.name === "File Upload")?.duration || 0;
    const backgroundRemovalTime =
      stepsRef.current.find((s) => s.name === "Background Removal")?.duration ||
      0;
    const renderTime =
      stepsRef.current.find((s) => s.name === "Image Render")?.duration || 0;

    const finalMetrics: ImageMetrics = {
      ...metrics,
      uploadTime,
      backgroundRemovalTime,
      renderTime,
      totalProcessingTime: totalTime,
      memoryUsage: {
        before: memoryRef.current.before,
        after: finalMemory,
        peak: memoryRef.current.peak,
      },
      steps: stepsRef.current,
    };

    setMetrics(finalMetrics);
    setIsTracking(false);

    // Generate recommendations
    generateRecommendations(finalMetrics);
  }, [metrics, getMemoryUsage]);

  // Generate performance recommendations using the enhanced system
  const generateRecommendations = useCallback((metrics: ImageMetrics) => {
    const recs = generatePerformanceRecommendations(metrics);
    const performanceInsights = generatePerformanceInsights(metrics);
    const score = calculatePerformanceScore(metrics);

    setRecommendations(recs);
    setInsights(performanceInsights);
    setPerformanceScore(score);
  }, []);

  // Reset tracking
  const resetTracking = useCallback(() => {
    setMetrics(null);
    setIsTracking(false);
    setRecommendations([]);
    setInsights([]);
    setPerformanceScore(0);
    stepsRef.current = [];
  }, []);

  return {
    metrics,
    isTracking,
    recommendations,
    insights,
    performanceScore,
    startTracking,
    addStep,
    completeStep,
    errorStep,
    finishTracking,
    resetTracking,
    getMemoryUsage,
    formatFileSize,
  };
};
