"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import SupabaseDebug from "./SupabaseDebug";
import NetworkDebug from "./NetworkDebug";
import ConsoleDebug from "./ConsoleDebug";
import ImagePerformanceDebug from "./ImagePerformanceDebug";
import PerformanceTestPanel from "./PerformanceTestPanel";
import StateValidationTest from "./StateValidationTest";
import {
  useImagePerformanceContext,
  useIsInPerformanceContext,
} from "@/contexts/ImagePerformanceContext";

const DebugDashboard = () => {
  const [isVisible, setIsVisible] = useState(false);
  const isInContext = useIsInPerformanceContext();

  // Only use context if available, otherwise show a message
  const contextData = isInContext ? useImagePerformanceContext() : null;
  const { metrics, isTracking, recommendations, insights, performanceScore } =
    contextData || {
      metrics: null,
      isTracking: false,
      recommendations: [],
      insights: [],
      performanceScore: 0,
    };

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsVisible(true)}
          className="bg-red-600 hover:bg-red-700 text-white"
        >
          🐛 Debug Panel
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg w-full max-w-6xl h-full max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-xl font-bold">🐛 Debug Dashboard</h2>
          <Button
            onClick={() => setIsVisible(false)}
            variant="outline"
            size="sm"
          >
            ✕ Close
          </Button>
        </div>

        <div className="p-4 h-full overflow-auto">
          <Tabs defaultValue="image-performance" className="w-full">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="image-performance">
                Image Performance
              </TabsTrigger>
              <TabsTrigger value="performance-test">
                Performance Test
              </TabsTrigger>
              <TabsTrigger value="supabase">Supabase</TabsTrigger>
              <TabsTrigger value="network">Network</TabsTrigger>
              <TabsTrigger value="console">Console</TabsTrigger>
              <TabsTrigger value="overview">Overview</TabsTrigger>
            </TabsList>

            <TabsContent value="image-performance" className="mt-4">
              <div className="space-y-4">
                <ImagePerformanceDebug
                  metrics={metrics}
                  isTracking={isTracking}
                  recommendations={recommendations}
                  insights={insights}
                  performanceScore={performanceScore}
                />
                {isInContext && <StateValidationTest />}
              </div>
            </TabsContent>

            <TabsContent value="performance-test" className="mt-4">
              <PerformanceTestPanel />
            </TabsContent>

            <TabsContent value="supabase" className="mt-4">
              <SupabaseDebug />
            </TabsContent>

            <TabsContent value="network" className="mt-4">
              <NetworkDebug />
            </TabsContent>

            <TabsContent value="console" className="mt-4">
              <ConsoleDebug />
            </TabsContent>

            <TabsContent value="overview" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>🔍 Debug Overview</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h3 className="font-semibold mb-2">
                      Common Issues & Solutions
                    </h3>
                    <div className="space-y-2 text-sm">
                      <div className="p-3 bg-red-50 border border-red-200 rounded">
                        <strong>401 Unauthorized Error:</strong>
                        <ul className="mt-1 ml-4 list-disc">
                          <li>
                            Check if NEXT_PUBLIC_SUPABASE_ANON_KEY is complete
                            (should be a valid JWT with 3 parts)
                          </li>
                          <li>
                            Verify NEXT_PUBLIC_SUPABASE_URL matches your project
                          </li>
                          <li>
                            Ensure Google OAuth is configured in Supabase
                            dashboard
                          </li>
                        </ul>
                      </div>

                      <div className="p-3 bg-yellow-50 border border-yellow-200 rounded">
                        <strong>OAuth Callback Issues:</strong>
                        <ul className="mt-1 ml-4 list-disc">
                          <li>Check redirect URLs in Google Cloud Console</li>
                          <li>
                            Verify callback route exists at /auth/callback
                          </li>
                          <li>Ensure site URL is set correctly in Supabase</li>
                        </ul>
                      </div>

                      <div className="p-3 bg-blue-50 border border-blue-200 rounded">
                        <strong>Database Connection Issues:</strong>
                        <ul className="mt-1 ml-4 list-disc">
                          <li>Check if RLS policies are properly configured</li>
                          <li>
                            Verify service role key for server-side operations
                          </li>
                          <li>
                            Ensure profiles table exists and is accessible
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold mb-2">
                      Environment Variables Checklist
                    </h3>
                    <div className="space-y-1 text-sm font-mono">
                      <div>✅ NEXT_PUBLIC_SUPABASE_URL</div>
                      <div>✅ NEXT_PUBLIC_SUPABASE_ANON_KEY (complete JWT)</div>
                      <div>
                        ⚠️ SUPABASE_SERVICE_ROLE_KEY (for server operations)
                      </div>
                      <div>⚠️ STRIPE_SECRET_KEY (for payments)</div>
                      <div>⚠️ STRIPE_WEBHOOK_SECRET (for webhooks)</div>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold mb-2">Quick Actions</h3>
                    <div className="flex gap-2">
                      <Button
                        onClick={() => window.location.reload()}
                        variant="outline"
                        size="sm"
                      >
                        🔄 Reload Page
                      </Button>
                      <Button
                        onClick={() => localStorage.clear()}
                        variant="outline"
                        size="sm"
                      >
                        🗑️ Clear Storage
                      </Button>
                      <Button
                        onClick={() => {
                          console.log("Environment Variables:", {
                            SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
                            HAS_ANON_KEY:
                              !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
                            ANON_KEY_LENGTH:
                              process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.length,
                          });
                        }}
                        variant="outline"
                        size="sm"
                      >
                        📋 Log Env Vars
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default DebugDashboard;
