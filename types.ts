export interface Profile {
  id: string;
  username: string;
  full_name: string;
  avatar_url: string;
  images_generated?: number; // Optional for backward compatibility
  paid?: boolean; // Optional for backward compatibility
  subscription_id?: string; // Optional for backward compatibility
}

export interface Design {
  id: number;
  creator: string;
  name: string;
  created_at: string;
  image: string;
  text_style: JSON[];
}
