// Direct database schema fix using Supabase REST API
const https = require('https');
const fs = require('fs');
require("dotenv").config({ path: ".env.local" });

async function makeSupabaseRequest(url, method, data, headers) {
  return new Promise((resolve, reject) => {
    const options = {
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    const req = https.request(url, options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = responseData ? JSON.parse(responseData) : {};
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: responseData });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function fixDatabaseSchema() {
  console.log("🔧 Applying direct database schema fix...\n");

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !serviceKey) {
    console.log("❌ Missing environment variables");
    console.log("NEXT_PUBLIC_SUPABASE_URL:", supabaseUrl ? "✅" : "❌");
    console.log("SUPABASE_SERVICE_ROLE_KEY:", serviceKey ? "✅" : "❌");
    return;
  }

  const headers = {
    'Authorization': `Bearer ${serviceKey}`,
    'apikey': serviceKey
  };

  try {
    // Step 1: Check current table structure
    console.log("📋 Checking current table structure...");
    
    const tableInfoUrl = `${supabaseUrl}/rest/v1/profiles?select=*&limit=1`;
    const tableCheck = await makeSupabaseRequest(tableInfoUrl, 'GET', null, headers);
    
    if (tableCheck.status === 200) {
      console.log("✅ Successfully connected to profiles table");
      if (tableCheck.data && tableCheck.data.length > 0) {
        console.log("Current columns:", Object.keys(tableCheck.data[0]));
      }
    } else {
      console.log("❌ Could not access profiles table:", tableCheck.data);
      return;
    }

    // Step 2: Try to add missing columns using SQL
    console.log("\n🔧 Adding missing columns...");
    
    const sqlCommands = [
      "ALTER TABLE profiles ADD COLUMN IF NOT EXISTS images_generated INTEGER DEFAULT 0;",
      "ALTER TABLE profiles ADD COLUMN IF NOT EXISTS paid BOOLEAN DEFAULT false;", 
      "ALTER TABLE profiles ADD COLUMN IF NOT EXISTS subscription_id TEXT DEFAULT '';"
    ];

    for (const sql of sqlCommands) {
      console.log(`Executing: ${sql}`);
      
      // Try using the SQL endpoint if it exists
      const sqlUrl = `${supabaseUrl}/rest/v1/rpc/exec_sql`;
      const sqlResult = await makeSupabaseRequest(sqlUrl, 'POST', { sql }, headers);
      
      if (sqlResult.status === 200 || sqlResult.status === 201) {
        console.log("✅ SQL executed successfully");
      } else {
        console.log(`⚠️ SQL execution result: ${sqlResult.status}`, sqlResult.data);
      }
    }

    // Step 3: Verify the changes
    console.log("\n📋 Verifying changes...");
    
    const verifyCheck = await makeSupabaseRequest(tableInfoUrl, 'GET', null, headers);
    if (verifyCheck.status === 200 && verifyCheck.data && verifyCheck.data.length > 0) {
      const columns = Object.keys(verifyCheck.data[0]);
      console.log("Updated columns:", columns);
      
      const requiredColumns = ['images_generated', 'paid', 'subscription_id'];
      const hasAllColumns = requiredColumns.every(col => columns.includes(col));
      
      if (hasAllColumns) {
        console.log("✅ All required columns are now present!");
      } else {
        const missing = requiredColumns.filter(col => !columns.includes(col));
        console.log("❌ Still missing columns:", missing);
      }
    }

    console.log("\n🎉 Schema fix attempt completed!");
    console.log("💡 If columns are still missing, please run the SQL manually in Supabase dashboard:");
    console.log("   1. Go to Supabase Dashboard → SQL Editor");
    console.log("   2. Run the SQL from scripts/fix-profiles-table.sql");

  } catch (error) {
    console.log("❌ Error during schema fix:", error.message);
  }
}

fixDatabaseSchema();
