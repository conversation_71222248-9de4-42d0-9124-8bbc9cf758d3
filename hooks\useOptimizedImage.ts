"use client";

import { useState, useCallback } from "react";
import {
  optimizeImage,
  createProgressiveImage,
  ImageOptimizationOptions,
  OptimizationResult,
  isWebPSupported,
} from "@/lib/imageOptimization";

export interface OptimizedImageState {
  original: File | null;
  thumbnail: OptimizationResult | null;
  preview: OptimizationResult | null;
  full: OptimizationResult | null;
  isOptimizing: boolean;
  error: string | null;
  stage: "idle" | "thumbnail" | "preview" | "full" | "complete";
}

export const useOptimizedImage = () => {
  const [state, setState] = useState<OptimizedImageState>({
    original: null,
    thumbnail: null,
    preview: null,
    full: null,
    isOptimizing: false,
    error: null,
    stage: "idle",
  });

  const optimizeImageFile = useCallback(
    async (
      file: File,
      options: ImageOptimizationOptions = {},
      onStepComplete?: (stepId: string, result: OptimizationResult) => void
    ) => {
      setState((prev) => ({
        ...prev,
        original: file,
        isOptimizing: true,
        error: null,
        stage: "thumbnail",
        thumbnail: null,
        preview: null,
        full: null,
      }));

      try {
        // Use progressive loading for better UX
        const results = await createProgressiveImage(file, (stage, result) => {
          setState((prev) => ({
            ...prev,
            [stage]: result,
            stage,
          }));

          // Notify performance monitoring
          onStepComplete?.(stage, result);
        });

        const [thumbnail, preview, full] = results;

        setState((prev) => ({
          ...prev,
          thumbnail,
          preview,
          full,
          isOptimizing: false,
          stage: "complete",
        }));

        return { thumbnail, preview, full };
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Optimization failed";
        setState((prev) => ({
          ...prev,
          isOptimizing: false,
          error: errorMessage,
          stage: "idle",
        }));
        throw error;
      }
    },
    []
  );

  const optimizeSingle = useCallback(
    async (
      file: File,
      options: ImageOptimizationOptions = {}
    ): Promise<OptimizationResult> => {
      setState((prev) => ({
        ...prev,
        original: file,
        isOptimizing: true,
        error: null,
      }));

      try {
        const result = await optimizeImage(file, options);

        setState((prev) => ({
          ...prev,
          full: result,
          isOptimizing: false,
          stage: "complete",
        }));

        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Optimization failed";
        setState((prev) => ({
          ...prev,
          isOptimizing: false,
          error: errorMessage,
        }));
        throw error;
      }
    },
    []
  );

  const reset = useCallback(() => {
    // Clean up blob URLs
    if (state.thumbnail?.url) URL.revokeObjectURL(state.thumbnail.url);
    if (state.preview?.url) URL.revokeObjectURL(state.preview.url);
    if (state.full?.url) URL.revokeObjectURL(state.full.url);

    setState({
      original: null,
      thumbnail: null,
      preview: null,
      full: null,
      isOptimizing: false,
      error: null,
      stage: "idle",
    });
  }, [state]);

  const getRecommendedOptions = useCallback(
    (file: File): ImageOptimizationOptions => {
      const fileSizeMB = file.size / (1024 * 1024);
      const webpSupported =
        typeof window !== "undefined" ? isWebPSupported() : false;

      // Adjust options based on file size and capabilities
      if (fileSizeMB > 10) {
        return {
          maxWidth: 1920,
          maxHeight: 1920,
          quality: 0.7,
          format: webpSupported ? "webp" : "jpeg",
        };
      } else if (fileSizeMB > 5) {
        return {
          maxWidth: 2048,
          maxHeight: 2048,
          quality: 0.75,
          format: webpSupported ? "webp" : "jpeg",
        };
      } else {
        return {
          maxWidth: 2048,
          maxHeight: 2048,
          quality: 0.8,
          format: webpSupported ? "webp" : "jpeg",
        };
      }
    },
    []
  );

  const getOptimizationStats = useCallback(() => {
    if (!state.original || !state.full) return null;

    return {
      originalSize: state.original.size,
      optimizedSize: state.full.optimizedSize,
      compressionRatio: state.full.compressionRatio,
      spaceSaved: state.original.size - state.full.optimizedSize,
      spaceSavedPercent: (
        ((state.original.size - state.full.optimizedSize) /
          state.original.size) *
        100
      ).toFixed(1),
      format: state.full.format,
      dimensions: state.full.dimensions,
    };
  }, [state.original, state.full]);

  // Memoize WebP support check to avoid calling on server
  const webPSupported = useCallback(() => {
    if (typeof window === "undefined") return false;
    return isWebPSupported();
  }, []);

  return {
    state,
    optimizeImageFile,
    optimizeSingle,
    reset,
    getRecommendedOptions,
    getOptimizationStats,
    isWebPSupported: webPSupported(),
  };
};
