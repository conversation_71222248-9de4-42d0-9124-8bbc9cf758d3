"use client";

import React, { useState } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useImagePerformance } from "@/hooks/useImagePerformance";
import { useOptimizedImage } from "@/hooks/useOptimizedImage";
import { TestTube, Upload, Zap, CheckCircle, AlertTriangle } from "lucide-react";

const PerformanceTestPanel: React.FC = () => {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isRunningTests, setIsRunningTests] = useState(false);
  
  const {
    metrics,
    recommendations,
    insights,
    performanceScore,
    startTracking,
    addStep,
    completeStep,
    finishTracking,
    resetTracking
  } = useImagePerformance();

  const {
    state: optimizationState,
    optimizeImageFile,
    getRecommendedOptions,
    getOptimizationStats
  } = useOptimizedImage();

  // Create test images of different sizes
  const createTestImage = (width: number, height: number, quality: number = 0.8): Promise<File> => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) return;

      canvas.width = width;
      canvas.height = height;

      // Create a gradient pattern for testing
      const gradient = ctx.createLinearGradient(0, 0, width, height);
      gradient.addColorStop(0, '#ff6b6b');
      gradient.addColorStop(0.5, '#4ecdc4');
      gradient.addColorStop(1, '#45b7d1');
      
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, width, height);

      // Add some text for complexity
      ctx.fillStyle = 'white';
      ctx.font = '48px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('Test Image', width / 2, height / 2);

      canvas.toBlob((blob) => {
        if (blob) {
          const file = new File([blob], `test-${width}x${height}.png`, { type: 'image/png' });
          resolve(file);
        }
      }, 'image/png', quality);
    });
  };

  const runPerformanceTest = async (testName: string, file: File) => {
    console.log(`Running test: ${testName}`);
    
    // Reset previous state
    resetTracking();
    
    // Start performance tracking
    startTracking(file);
    
    const uploadStepId = addStep('Test File Upload', {
      fileName: file.name,
      fileSize: file.size,
      testName
    });

    try {
      // Simulate image optimization
      const options = getRecommendedOptions(file);
      const optimizationStepId = addStep('Test Optimization');
      
      await optimizeImageFile(file, options);
      
      completeStep(optimizationStepId, getOptimizationStats());
      completeStep(uploadStepId);
      
      // Simulate background removal delay
      const bgStepId = addStep('Simulated Background Removal');
      await new Promise(resolve => setTimeout(resolve, Math.random() * 3000 + 1000));
      completeStep(bgStepId);
      
      finishTracking();
      
      return {
        testName,
        fileSize: file.size,
        metrics: metrics,
        recommendations: recommendations,
        performanceScore: performanceScore,
        success: true
      };
    } catch (error) {
      return {
        testName,
        fileSize: file.size,
        error: error instanceof Error ? error.message : 'Unknown error',
        success: false
      };
    }
  };

  const runAllTests = async () => {
    setIsRunningTests(true);
    setTestResults([]);
    
    const tests = [
      { name: 'Small Image (512x512)', width: 512, height: 512 },
      { name: 'Medium Image (1920x1080)', width: 1920, height: 1080 },
      { name: 'Large Image (4000x3000)', width: 4000, height: 3000 },
      { name: 'Ultra Large Image (6000x4000)', width: 6000, height: 4000 }
    ];

    const results = [];
    
    for (const test of tests) {
      try {
        const testFile = await createTestImage(test.width, test.height);
        const result = await runPerformanceTest(test.name, testFile);
        results.push(result);
        setTestResults([...results]);
        
        // Small delay between tests
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (error) {
        results.push({
          testName: test.name,
          error: error instanceof Error ? error.message : 'Test failed',
          success: false
        });
        setTestResults([...results]);
      }
    }
    
    setIsRunningTests(false);
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadge = (score: number) => {
    if (score >= 80) return 'success';
    if (score >= 60) return 'warning';
    return 'destructive';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TestTube className="h-5 w-5" />
          Performance Testing Panel
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Button 
            onClick={runAllTests} 
            disabled={isRunningTests}
            className="flex items-center gap-2"
          >
            {isRunningTests ? (
              <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
            ) : (
              <Zap className="h-4 w-4" />
            )}
            {isRunningTests ? 'Running Tests...' : 'Run Performance Tests'}
          </Button>
        </div>

        {testResults.length > 0 && (
          <div className="space-y-3">
            <h3 className="font-semibold">Test Results</h3>
            {testResults.map((result, index) => (
              <Card key={index} className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium">{result.testName}</span>
                  {result.success ? (
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      {result.performanceScore && (
                        <Badge variant={getScoreBadge(result.performanceScore)}>
                          Score: {result.performanceScore}
                        </Badge>
                      )}
                    </div>
                  ) : (
                    <AlertTriangle className="h-4 w-4 text-red-500" />
                  )}
                </div>
                
                {result.success ? (
                  <div className="space-y-2 text-sm">
                    <div>File Size: {(result.fileSize / 1024 / 1024).toFixed(2)} MB</div>
                    {result.metrics && (
                      <div>
                        Processing Time: {(result.metrics.totalProcessingTime / 1000).toFixed(2)}s
                      </div>
                    )}
                    {result.recommendations && result.recommendations.length > 0 && (
                      <div>
                        <span className="font-medium">Recommendations: </span>
                        {result.recommendations.length} issues found
                      </div>
                    )}
                  </div>
                ) : (
                  <Alert>
                    <AlertDescription>
                      Error: {result.error}
                    </AlertDescription>
                  </Alert>
                )}
              </Card>
            ))}
          </div>
        )}

        <Alert>
          <AlertDescription>
            This panel creates test images of various sizes and runs them through the performance monitoring system to validate that the debug panel provides accurate metrics and recommendations.
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  );
};

export default PerformanceTestPanel;
