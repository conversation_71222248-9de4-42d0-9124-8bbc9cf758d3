"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import {
  Clock,
  Image as ImageIcon,
  Zap,
  MemoryStick,
  FileImage,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Info,
  Upload,
} from "lucide-react";
import {
  ImageMetrics,
  ProcessingStep,
  PerformanceRecommendation,
} from "@/hooks/useImagePerformance";
import { PerformanceInsight } from "@/lib/performanceRecommendations";

interface ImagePerformanceDebugProps {
  metrics: ImageMetrics | null;
  isTracking: boolean;
  recommendations: PerformanceRecommendation[];
  insights?: PerformanceInsight[];
  performanceScore?: number;
}

const ImagePerformanceDebug: React.FC<ImagePerformanceDebugProps> = ({
  metrics,
  isTracking,
  recommendations,
  insights = [],
  performanceScore = 0,
}) => {
  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms.toFixed(0)}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const getStatusIcon = (status: ProcessingStep["status"]) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "error":
        return <XCircle className="h-4 w-4 text-red-500" />;
      case "in-progress":
        return (
          <div className="h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
        );
      default:
        return <div className="h-4 w-4 bg-gray-300 rounded-full" />;
    }
  };

  const getRecommendationIcon = (type: PerformanceRecommendation["type"]) => {
    switch (type) {
      case "error":
        return <XCircle className="h-4 w-4 text-red-500" />;
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      default:
        return <Info className="h-4 w-4 text-blue-500" />;
    }
  };

  const getImpactColor = (impact: PerformanceRecommendation["impact"]) => {
    switch (impact) {
      case "high":
        return "bg-red-100 text-red-800 border-red-200";
      case "medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      default:
        return "bg-blue-100 text-blue-800 border-blue-200";
    }
  };

  // Show different states based on tracking and metrics
  if (!metrics && !isTracking) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ImageIcon className="h-5 w-5" />
            Image Performance Monitor
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <div className="flex flex-col items-center gap-2">
              <Upload className="h-8 w-8 opacity-50" />
              <p>Upload an image to start performance monitoring</p>
              <p className="text-sm">
                The debug panel will track processing times, memory usage, and
                provide optimization recommendations.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Show loading state when tracking but no metrics yet
  if (isTracking && !metrics) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ImageIcon className="h-5 w-5" />
            Image Performance Monitor
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <div className="flex flex-col items-center gap-3">
              <div className="h-8 w-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
              <p className="text-muted-foreground">
                Initializing performance tracking...
              </p>
              <p className="text-sm text-muted-foreground">
                Setting up monitoring for your image upload
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <FileImage className="h-4 w-4 text-blue-500" />
              <div className="text-sm font-medium">File Size</div>
            </div>
            <div className="text-2xl font-bold">
              {metrics?.fileSizeFormatted ||
                (isTracking ? "Loading..." : "N/A")}
            </div>
            {metrics && (
              <div className="text-xs text-muted-foreground">
                {metrics.dimensions.width} × {metrics.dimensions.height}px
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-green-500" />
              <div className="text-sm font-medium">Total Time</div>
            </div>
            <div className="text-2xl font-bold">
              {metrics
                ? formatTime(metrics.totalProcessingTime)
                : isTracking
                ? "Processing..."
                : "N/A"}
            </div>
            {metrics && (
              <div className="text-xs text-muted-foreground">
                Background: {formatTime(metrics.backgroundRemovalTime)}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <MemoryStick className="h-4 w-4 text-purple-500" />
              <div className="text-sm font-medium">Memory Usage</div>
            </div>
            <div className="text-2xl font-bold">
              {metrics
                ? `${(
                    metrics.memoryUsage.peak - metrics.memoryUsage.before
                  ).toFixed(1)}MB`
                : isTracking
                ? "Monitoring..."
                : "N/A"}
            </div>
            {metrics && (
              <div className="text-xs text-muted-foreground">
                Peak: {metrics.memoryUsage.peak.toFixed(1)}MB
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4 text-orange-500" />
              <div className="text-sm font-medium">Performance</div>
            </div>
            <div className="text-2xl font-bold">
              {recommendations.length > 0
                ? recommendations.find((r) => r.type === "success")
                  ? "Good"
                  : "Issues"
                : isTracking
                ? "Analyzing..."
                : "N/A"}
            </div>
            {recommendations.length > 0 && (
              <div className="text-xs text-muted-foreground">
                {recommendations.length} recommendation
                {recommendations.length !== 1 ? "s" : ""}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Processing Steps */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Processing Pipeline
          </CardTitle>
        </CardHeader>
        <CardContent>
          {metrics?.steps && metrics.steps.length > 0 ? (
            <div className="space-y-3">
              {metrics.steps.map((step, index) => (
                <div
                  key={step.id}
                  className="flex items-center gap-3 p-3 border rounded-lg"
                >
                  <div className="flex-shrink-0">
                    {getStatusIcon(step.status)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{step.name}</span>
                      <Badge variant="outline">
                        {step.duration
                          ? formatTime(step.duration)
                          : "In progress..."}
                      </Badge>
                    </div>
                    {step.memorySnapshot && (
                      <div className="text-xs text-muted-foreground mt-1">
                        Memory: {step.memorySnapshot.toFixed(1)}MB
                      </div>
                    )}
                    {step.status === "error" && step.details?.error && (
                      <div className="text-xs text-red-600 mt-1">
                        Error: {step.details.error}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-4 text-muted-foreground">
              {isTracking
                ? "Processing steps will appear here..."
                : "No processing steps recorded"}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Performance Recommendations */}
      {recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Performance Recommendations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recommendations.map((rec, index) => (
                <Alert
                  key={index}
                  className={`border ${getImpactColor(rec.impact)}`}
                >
                  <div className="flex items-start gap-3">
                    {getRecommendationIcon(rec.type)}
                    <div className="flex-1">
                      <AlertTitle className="flex items-center gap-2">
                        {rec.title}
                        <Badge variant="outline" className="text-xs">
                          {rec.impact} impact
                        </Badge>
                      </AlertTitle>
                      <AlertDescription className="mt-2">
                        <div className="mb-2">{rec.description}</div>
                        <div className="text-sm font-medium">Solution:</div>
                        <div className="text-sm">{rec.solution}</div>
                      </AlertDescription>
                    </div>
                  </div>
                </Alert>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Detailed Metrics */}
      {metrics && (
        <Card>
          <CardHeader>
            <CardTitle>Detailed Metrics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium mb-2">Image Information</h4>
                <div className="space-y-1 text-sm">
                  <div>Format: {metrics.format}</div>
                  <div>
                    Dimensions: {metrics.dimensions.width} ×{" "}
                    {metrics.dimensions.height}px
                  </div>
                  <div>File Size: {metrics.fileSizeFormatted}</div>
                </div>
              </div>
              <div>
                <h4 className="font-medium mb-2">Timing Breakdown</h4>
                <div className="space-y-1 text-sm">
                  <div>Upload: {formatTime(metrics.uploadTime)}</div>
                  <div>
                    Background Removal:{" "}
                    {formatTime(metrics.backgroundRemovalTime)}
                  </div>
                  <div>Rendering: {formatTime(metrics.renderTime)}</div>
                  <div>Total: {formatTime(metrics.totalProcessingTime)}</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ImagePerformanceDebug;
