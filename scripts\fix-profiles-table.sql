-- Fix profiles table schema to match application requirements
-- This script adds the missing columns that the application expects

-- Add missing columns to profiles table
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS images_generated INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS paid BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS subscription_id TEXT DEFAULT '';

-- Update existing records to have default values for new columns
UPDATE profiles 
SET 
  images_generated = COALESCE(images_generated, 0),
  paid = COALESCE(paid, false),
  subscription_id = COALESCE(subscription_id, '')
WHERE 
  images_generated IS NULL 
  OR paid IS NULL 
  OR subscription_id IS NULL;

-- Add constraints to ensure data integrity
ALTER TABLE profiles 
ALTER COLUMN images_generated SET NOT NULL,
ALTER COLUMN paid SET NOT NULL,
ALTER COLUMN subscription_id SET NOT NULL;

-- Create index on subscription_id for better performance
CREATE INDEX IF NOT EXISTS idx_profiles_subscription_id ON profiles(subscription_id);

-- Create index on paid status for better performance
CREATE INDEX IF NOT EXISTS idx_profiles_paid ON profiles(paid);

-- Verify the table structure
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'profiles' 
ORDER BY ordinal_position;
