// Simple test to check if we can update the database schema
const { createClient } = require("@supabase/supabase-js");
require("dotenv").config({ path: ".env.local" });

async function testSchemaUpdate() {
  console.log("🔍 Testing schema update approach...\n");

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    console.log("❌ Missing environment variables");
    return;
  }

  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  try {
    // First, let's check what columns currently exist
    console.log("📋 Checking current table structure...");
    
    // Try to select from profiles table to see current structure
    const { data: testData, error: testError } = await supabase
      .from('profiles')
      .select('*')
      .limit(1);

    if (testError) {
      console.log("❌ Error accessing profiles table:", testError.message);
      return;
    }

    console.log("✅ Successfully accessed profiles table");
    
    if (testData && testData.length > 0) {
      console.log("📋 Current columns in first record:");
      console.log(Object.keys(testData[0]));
    } else {
      console.log("📋 No records found in profiles table");
    }

    // Try to insert a test record with all expected fields
    console.log("\n🧪 Testing insert with all expected fields...");
    
    const testProfile = {
      id: 'test-user-' + Date.now(),
      username: 'Test User',
      full_name: 'Test User Full Name',
      avatar_url: 'https://example.com/avatar.jpg',
      images_generated: 0,
      paid: false,
      subscription_id: ''
    };

    const { data: insertData, error: insertError } = await supabase
      .from('profiles')
      .insert([testProfile])
      .select();

    if (insertError) {
      console.log("❌ Insert test failed:", insertError.message);
      console.log("This confirms the schema mismatch issue");
      
      // Try to identify which columns are missing
      if (insertError.message.includes('column') && insertError.message.includes('does not exist')) {
        const match = insertError.message.match(/column "([^"]+)" of relation "profiles" does not exist/);
        if (match) {
          console.log(`🔍 Missing column identified: ${match[1]}`);
        }
      }
    } else {
      console.log("✅ Insert test successful - schema appears to be correct");
      console.log("Inserted record:", insertData);
      
      // Clean up test record
      await supabase
        .from('profiles')
        .delete()
        .eq('id', testProfile.id);
      console.log("🧹 Cleaned up test record");
    }

  } catch (error) {
    console.log("❌ Test failed:", error.message);
  }
}

testSchemaUpdate();
