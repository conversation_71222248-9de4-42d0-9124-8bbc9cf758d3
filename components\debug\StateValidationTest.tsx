"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useImagePerformanceContext } from "@/contexts/ImagePerformanceContext";
import { CheckCircle, XCircle, AlertTriangle } from "lucide-react";

const StateValidationTest: React.FC = () => {
  const { metrics, isTracking, recommendations } = useImagePerformanceContext();

  const getStateStatus = () => {
    if (isTracking && !metrics) {
      return {
        status: 'tracking-no-metrics',
        label: 'Tracking (No Metrics)',
        color: 'bg-blue-100 text-blue-800',
        icon: <div className="h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />,
        description: 'Performance tracking is active but metrics are not yet available'
      };
    }
    
    if (isTracking && metrics) {
      return {
        status: 'tracking-with-metrics',
        label: 'Tracking (With Metrics)',
        color: 'bg-green-100 text-green-800',
        icon: <CheckCircle className="h-4 w-4 text-green-500" />,
        description: 'Performance tracking is active and metrics are available'
      };
    }
    
    if (!isTracking && metrics) {
      return {
        status: 'complete',
        label: 'Complete',
        color: 'bg-green-100 text-green-800',
        icon: <CheckCircle className="h-4 w-4 text-green-500" />,
        description: 'Performance tracking completed with metrics available'
      };
    }
    
    return {
      status: 'idle',
      label: 'Idle',
      color: 'bg-gray-100 text-gray-800',
      icon: <XCircle className="h-4 w-4 text-gray-500" />,
      description: 'No performance tracking active'
    };
  };

  const stateInfo = getStateStatus();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5" />
          State Validation Test
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-3">
          {stateInfo.icon}
          <div>
            <Badge className={stateInfo.color}>
              {stateInfo.label}
            </Badge>
            <p className="text-sm text-muted-foreground mt-1">
              {stateInfo.description}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <strong>Is Tracking:</strong> {isTracking ? 'Yes' : 'No'}
          </div>
          <div>
            <strong>Has Metrics:</strong> {metrics ? 'Yes' : 'No'}
          </div>
          <div>
            <strong>Recommendations:</strong> {recommendations.length}
          </div>
          <div>
            <strong>State Status:</strong> {stateInfo.status}
          </div>
        </div>

        {metrics && (
          <div className="border-t pt-4">
            <h4 className="font-medium mb-2">Current Metrics</h4>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div>File Size: {metrics.fileSizeFormatted}</div>
              <div>Total Time: {(metrics.totalProcessingTime / 1000).toFixed(2)}s</div>
              <div>Steps: {metrics.steps.length}</div>
              <div>Format: {metrics.format}</div>
            </div>
          </div>
        )}

        <div className="text-xs text-muted-foreground border-t pt-2">
          <strong>Expected Behavior:</strong>
          <ul className="list-disc list-inside mt-1 space-y-1">
            <li>Should show "Idle" when no image is being processed</li>
            <li>Should show "Tracking (No Metrics)" immediately after upload starts</li>
            <li>Should show "Tracking (With Metrics)" during processing</li>
            <li>Should show "Complete" when processing finishes</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default StateValidationTest;
