"use client";

export interface ImageOptimizationOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: "webp" | "jpeg" | "png";
  enableProgressive?: boolean;
}

export interface OptimizationResult {
  blob: Blob;
  url: string;
  originalSize: number;
  optimizedSize: number;
  compressionRatio: number;
  format: string;
  dimensions: { width: number; height: number };
}

// Cache for processed images
const imageCache = new Map<string, OptimizationResult>();

// Generate cache key from file and options
const generateCacheKey = (
  file: File,
  options: ImageOptimizationOptions
): string => {
  const optionsStr = JSON.stringify(options);
  return `${file.name}-${file.size}-${file.lastModified}-${btoa(optionsStr)}`;
};

// Check if WebP is supported
export const isWebPSupported = (): boolean => {
  // Check if we're in a browser environment
  if (typeof window === "undefined" || typeof document === "undefined") {
    return false; // Default to false on server-side
  }

  try {
    const canvas = document.createElement("canvas");
    canvas.width = 1;
    canvas.height = 1;
    return canvas.toDataURL("image/webp").indexOf("data:image/webp") === 0;
  } catch (error) {
    return false;
  }
};

// Compress and optimize image
export const optimizeImage = async (
  file: File,
  options: ImageOptimizationOptions = {}
): Promise<OptimizationResult> => {
  const {
    maxWidth = 2048,
    maxHeight = 2048,
    quality = 0.8,
    format = typeof window !== "undefined" && isWebPSupported()
      ? "webp"
      : "jpeg",
    enableProgressive = true,
  } = options;

  // Check cache first
  const cacheKey = generateCacheKey(file, options);
  if (imageCache.has(cacheKey)) {
    return imageCache.get(cacheKey)!;
  }

  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      try {
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");
        if (!ctx) {
          reject(new Error("Could not get canvas context"));
          return;
        }

        // Calculate new dimensions
        let { width, height } = img;
        const aspectRatio = width / height;

        if (width > maxWidth) {
          width = maxWidth;
          height = width / aspectRatio;
        }
        if (height > maxHeight) {
          height = maxHeight;
          width = height * aspectRatio;
        }

        // Set canvas dimensions
        canvas.width = width;
        canvas.height = height;

        // Enable image smoothing for better quality
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = "high";

        // Draw and compress
        ctx.drawImage(img, 0, 0, width, height);

        // Convert to blob
        canvas.toBlob(
          (blob) => {
            if (!blob) {
              reject(new Error("Failed to create blob"));
              return;
            }

            const result: OptimizationResult = {
              blob,
              url: URL.createObjectURL(blob),
              originalSize: file.size,
              optimizedSize: blob.size,
              compressionRatio: file.size / blob.size,
              format: format.toUpperCase(),
              dimensions: { width, height },
            };

            // Cache the result
            imageCache.set(cacheKey, result);

            resolve(result);
          },
          `image/${format}`,
          quality
        );
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      reject(new Error("Failed to load image"));
    };

    img.src = URL.createObjectURL(file);
  });
};

// Progressive image loading
export const createProgressiveImage = (
  file: File,
  onProgress?: (
    stage: "thumbnail" | "preview" | "full",
    result: OptimizationResult
  ) => void
): Promise<OptimizationResult[]> => {
  return Promise.all([
    // Thumbnail (64px max)
    optimizeImage(file, { maxWidth: 64, maxHeight: 64, quality: 0.6 }),
    // Preview (512px max)
    optimizeImage(file, { maxWidth: 512, maxHeight: 512, quality: 0.7 }),
    // Full size (2048px max)
    optimizeImage(file, { maxWidth: 2048, maxHeight: 2048, quality: 0.8 }),
  ]).then(([thumbnail, preview, full]) => {
    onProgress?.("thumbnail", thumbnail);
    setTimeout(() => onProgress?.("preview", preview), 100);
    setTimeout(() => onProgress?.("full", full), 200);
    return [thumbnail, preview, full];
  });
};

// Batch optimize multiple images
export const batchOptimizeImages = async (
  files: File[],
  options: ImageOptimizationOptions = {},
  onProgress?: (
    completed: number,
    total: number,
    current: OptimizationResult
  ) => void
): Promise<OptimizationResult[]> => {
  const results: OptimizationResult[] = [];

  for (let i = 0; i < files.length; i++) {
    try {
      const result = await optimizeImage(files[i], options);
      results.push(result);
      onProgress?.(i + 1, files.length, result);
    } catch (error) {
      console.error(`Failed to optimize image ${files[i].name}:`, error);
    }
  }

  return results;
};

// Clean up cached URLs to prevent memory leaks
export const cleanupImageCache = (maxAge: number = 5 * 60 * 1000): void => {
  const now = Date.now();
  for (const [key, result] of imageCache.entries()) {
    // Simple cleanup based on cache size (in production, you'd want timestamp-based cleanup)
    if (imageCache.size > 50) {
      URL.revokeObjectURL(result.url);
      imageCache.delete(key);
    }
  }
};

// Get cache statistics
export const getCacheStats = () => {
  const totalSize = Array.from(imageCache.values()).reduce(
    (sum, result) => sum + result.optimizedSize,
    0
  );

  return {
    count: imageCache.size,
    totalSize,
    totalSizeFormatted: formatBytes(totalSize),
  };
};

// Format bytes utility
const formatBytes = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

// Preload image for better UX
export const preloadImage = (url: string): Promise<HTMLImageElement> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.src = url;
  });
};

// Convert image to different format
export const convertImageFormat = async (
  file: File,
  targetFormat: "webp" | "jpeg" | "png",
  quality: number = 0.8
): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      if (!ctx) {
        reject(new Error("Could not get canvas context"));
        return;
      }

      canvas.width = img.naturalWidth;
      canvas.height = img.naturalHeight;
      ctx.drawImage(img, 0, 0);

      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error("Failed to convert image"));
          }
        },
        `image/${targetFormat}`,
        quality
      );
    };
    img.onerror = reject;
    img.src = URL.createObjectURL(file);
  });
};

// Auto-cleanup on page unload
if (typeof window !== "undefined") {
  window.addEventListener("beforeunload", () => {
    for (const result of imageCache.values()) {
      URL.revokeObjectURL(result.url);
    }
    imageCache.clear();
  });
}
