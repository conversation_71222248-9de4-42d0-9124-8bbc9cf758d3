"use client";

import React, { createContext, useContext, ReactNode } from 'react';
import { useImagePerformance } from '@/hooks/useImagePerformance';
import { ImageMetrics, ProcessingStep, PerformanceRecommendation } from '@/hooks/useImagePerformance';
import { PerformanceInsight } from '@/lib/performanceRecommendations';

interface ImagePerformanceContextType {
  // State
  metrics: ImageMetrics | null;
  isTracking: boolean;
  recommendations: PerformanceRecommendation[];
  insights: PerformanceInsight[];
  performanceScore: number;
  
  // Actions
  startTracking: (file: File) => void;
  addStep: (name: string, details?: any) => string;
  completeStep: (stepId: string, details?: any) => void;
  errorStep: (stepId: string, error: any) => void;
  finishTracking: () => void;
  resetTracking: () => void;
  
  // Utilities
  getMemoryUsage: () => number;
  formatFileSize: (bytes: number) => string;
}

const ImagePerformanceContext = createContext<ImagePerformanceContextType | undefined>(undefined);

interface ImagePerformanceProviderProps {
  children: ReactNode;
}

export const ImagePerformanceProvider: React.FC<ImagePerformanceProviderProps> = ({ children }) => {
  const performanceHook = useImagePerformance();

  return (
    <ImagePerformanceContext.Provider value={performanceHook}>
      {children}
    </ImagePerformanceContext.Provider>
  );
};

export const useImagePerformanceContext = (): ImagePerformanceContextType => {
  const context = useContext(ImagePerformanceContext);
  if (context === undefined) {
    throw new Error('useImagePerformanceContext must be used within an ImagePerformanceProvider');
  }
  return context;
};

// Hook to check if we're within the performance context
export const useIsInPerformanceContext = (): boolean => {
  const context = useContext(ImagePerformanceContext);
  return context !== undefined;
};
