"use client";

import { ImageMetrics, PerformanceRecommendation } from "@/hooks/useImagePerformance";

export interface RecommendationRule {
  id: string;
  name: string;
  condition: (metrics: ImageMetrics) => boolean;
  recommendation: (metrics: ImageMetrics) => PerformanceRecommendation;
  priority: number; // Higher number = higher priority
}

export interface PerformanceInsight {
  category: 'file-size' | 'dimensions' | 'processing' | 'memory' | 'format' | 'optimization';
  severity: 'critical' | 'warning' | 'info' | 'success';
  title: string;
  description: string;
  impact: string;
  solution: string;
  technicalDetails?: string;
  estimatedImprovement?: string;
}

// Performance analysis rules
const performanceRules: RecommendationRule[] = [
  {
    id: 'large-file-size',
    name: 'Large File Size Detection',
    priority: 9,
    condition: (metrics) => metrics.fileSize > 10 * 1024 * 1024, // 10MB
    recommendation: (metrics) => ({
      type: 'error',
      title: 'Very Large File Size',
      description: `Image size is ${metrics.fileSizeFormatted}. Files over 10MB can cause significant performance issues.`,
      impact: 'high',
      solution: 'Compress the image to under 5MB or use progressive loading. Consider using WebP format for 25-35% better compression.'
    })
  },
  {
    id: 'medium-file-size',
    name: 'Medium File Size Warning',
    priority: 6,
    condition: (metrics) => metrics.fileSize > 5 * 1024 * 1024 && metrics.fileSize <= 10 * 1024 * 1024,
    recommendation: (metrics) => ({
      type: 'warning',
      title: 'Large File Size',
      description: `Image size is ${metrics.fileSizeFormatted}. Consider optimization for better performance.`,
      impact: 'medium',
      solution: 'Compress the image or convert to WebP format. Target file size under 2MB for optimal web performance.'
    })
  },
  {
    id: 'high-resolution',
    name: 'High Resolution Detection',
    priority: 7,
    condition: (metrics) => metrics.dimensions.width > 4000 || metrics.dimensions.height > 4000,
    recommendation: (metrics) => ({
      type: 'warning',
      title: 'Very High Resolution',
      description: `Image dimensions are ${metrics.dimensions.width}×${metrics.dimensions.height}px. Ultra-high resolution images require more processing power.`,
      impact: 'medium',
      solution: 'Resize to maximum 2048px for web use. Use responsive images with multiple sizes for different screen densities.'
    })
  },
  {
    id: 'slow-background-removal',
    name: 'Background Removal Performance',
    priority: 10,
    condition: (metrics) => metrics.backgroundRemovalTime > 15000, // 15 seconds
    recommendation: (metrics) => ({
      type: 'error',
      title: 'Very Slow Background Removal',
      description: `Background removal took ${(metrics.backgroundRemovalTime / 1000).toFixed(2)} seconds. This is significantly slower than expected.`,
      impact: 'high',
      solution: 'Consider pre-processing images on the server, implementing progressive processing, or using a more efficient background removal service.'
    })
  },
  {
    id: 'moderate-bg-removal',
    name: 'Moderate Background Removal Time',
    priority: 5,
    condition: (metrics) => metrics.backgroundRemovalTime > 8000 && metrics.backgroundRemovalTime <= 15000,
    recommendation: (metrics) => ({
      type: 'warning',
      title: 'Slow Background Removal',
      description: `Background removal took ${(metrics.backgroundRemovalTime / 1000).toFixed(2)} seconds.`,
      impact: 'medium',
      solution: 'Consider optimizing image size before processing or implementing caching for repeated operations.'
    })
  },
  {
    id: 'high-memory-usage',
    name: 'High Memory Usage',
    priority: 8,
    condition: (metrics) => {
      const memoryIncrease = metrics.memoryUsage.peak - metrics.memoryUsage.before;
      return memoryIncrease > 200; // 200MB
    },
    recommendation: (metrics) => ({
      type: 'error',
      title: 'Excessive Memory Usage',
      description: `Processing increased memory usage by ${(metrics.memoryUsage.peak - metrics.memoryUsage.before).toFixed(2)}MB.`,
      impact: 'high',
      solution: 'Implement image streaming, reduce image resolution before processing, or use chunked processing to reduce memory footprint.'
    })
  },
  {
    id: 'moderate-memory-usage',
    name: 'Moderate Memory Usage',
    priority: 4,
    condition: (metrics) => {
      const memoryIncrease = metrics.memoryUsage.peak - metrics.memoryUsage.before;
      return memoryIncrease > 100 && memoryIncrease <= 200; // 100-200MB
    },
    recommendation: (metrics) => ({
      type: 'warning',
      title: 'High Memory Usage',
      description: `Processing increased memory usage by ${(metrics.memoryUsage.peak - metrics.memoryUsage.before).toFixed(2)}MB.`,
      impact: 'medium',
      solution: 'Consider optimizing image size or implementing progressive processing to reduce memory usage.'
    })
  },
  {
    id: 'slow-total-processing',
    name: 'Slow Overall Processing',
    priority: 9,
    condition: (metrics) => metrics.totalProcessingTime > 20000, // 20 seconds
    recommendation: (metrics) => ({
      type: 'error',
      title: 'Very Slow Processing',
      description: `Total processing time was ${(metrics.totalProcessingTime / 1000).toFixed(2)} seconds.`,
      impact: 'high',
      solution: 'Implement caching, progressive loading, optimize image preprocessing, or consider server-side processing for better performance.'
    })
  },
  {
    id: 'png-format-optimization',
    name: 'PNG Format Optimization',
    priority: 3,
    condition: (metrics) => metrics.format === 'PNG' && metrics.fileSize > 2 * 1024 * 1024,
    recommendation: (metrics) => ({
      type: 'info',
      title: 'PNG Format Detected',
      description: `Large PNG file (${metrics.fileSizeFormatted}) detected. PNG is great for images with transparency but can be large.`,
      impact: 'low',
      solution: 'Consider converting to WebP for better compression while maintaining quality, or JPEG if transparency is not needed.'
    })
  },
  {
    id: 'good-performance',
    name: 'Good Performance Indicator',
    priority: 1,
    condition: (metrics) => {
      return metrics.totalProcessingTime < 10000 && 
             metrics.fileSize < 5 * 1024 * 1024 &&
             (metrics.memoryUsage.peak - metrics.memoryUsage.before) < 100;
    },
    recommendation: (metrics) => ({
      type: 'success',
      title: 'Excellent Performance',
      description: 'Image processing completed efficiently with optimal performance metrics.',
      impact: 'low',
      solution: 'Continue monitoring performance. Consider this as a baseline for future optimizations.'
    })
  }
];

// Generate performance recommendations
export const generatePerformanceRecommendations = (metrics: ImageMetrics): PerformanceRecommendation[] => {
  const recommendations: PerformanceRecommendation[] = [];
  
  // Apply all matching rules
  for (const rule of performanceRules) {
    if (rule.condition(metrics)) {
      recommendations.push(rule.recommendation(metrics));
    }
  }
  
  // Sort by priority (higher priority first)
  const ruleMap = new Map(performanceRules.map(rule => [rule.recommendation(metrics).title, rule.priority]));
  recommendations.sort((a, b) => (ruleMap.get(b.title) || 0) - (ruleMap.get(a.title) || 0));
  
  return recommendations;
};

// Generate detailed performance insights
export const generatePerformanceInsights = (metrics: ImageMetrics): PerformanceInsight[] => {
  const insights: PerformanceInsight[] = [];
  
  // File size analysis
  if (metrics.fileSize > 10 * 1024 * 1024) {
    insights.push({
      category: 'file-size',
      severity: 'critical',
      title: 'Critical File Size Issue',
      description: `File size of ${metrics.fileSizeFormatted} is extremely large for web use`,
      impact: 'Slow loading, high bandwidth usage, poor user experience',
      solution: 'Compress image to under 2MB, use progressive JPEG, or implement lazy loading',
      technicalDetails: 'Files over 10MB can cause browser memory issues and slow network transfers',
      estimatedImprovement: 'Up to 80% faster loading with proper compression'
    });
  }
  
  // Processing time analysis
  if (metrics.backgroundRemovalTime > 15000) {
    insights.push({
      category: 'processing',
      severity: 'critical',
      title: 'Background Removal Bottleneck',
      description: `Background removal took ${(metrics.backgroundRemovalTime / 1000).toFixed(2)}s`,
      impact: 'Poor user experience, potential timeouts, high CPU usage',
      solution: 'Implement server-side processing, use WebAssembly optimization, or progressive processing',
      technicalDetails: 'Client-side background removal is CPU intensive and blocks the main thread',
      estimatedImprovement: '70% faster processing with server-side implementation'
    });
  }
  
  // Memory usage analysis
  const memoryIncrease = metrics.memoryUsage.peak - metrics.memoryUsage.before;
  if (memoryIncrease > 200) {
    insights.push({
      category: 'memory',
      severity: 'warning',
      title: 'High Memory Consumption',
      description: `Processing consumed ${memoryIncrease.toFixed(2)}MB of additional memory`,
      impact: 'Potential browser crashes on low-memory devices, slower performance',
      solution: 'Implement image streaming, reduce resolution before processing, use Web Workers',
      technicalDetails: 'Large images are loaded entirely into memory during processing',
      estimatedImprovement: '60% reduction in memory usage with streaming approach'
    });
  }
  
  // Format optimization
  if (metrics.format === 'PNG' && metrics.fileSize > 5 * 1024 * 1024) {
    insights.push({
      category: 'format',
      severity: 'info',
      title: 'Format Optimization Opportunity',
      description: 'Large PNG file could benefit from format conversion',
      impact: 'Larger file sizes, slower loading times',
      solution: 'Convert to WebP for 25-35% better compression, or JPEG if transparency not needed',
      technicalDetails: 'PNG uses lossless compression which results in larger files',
      estimatedImprovement: '25-50% file size reduction with WebP conversion'
    });
  }
  
  // Success case
  if (insights.length === 0) {
    insights.push({
      category: 'optimization',
      severity: 'success',
      title: 'Optimal Performance Achieved',
      description: 'All performance metrics are within acceptable ranges',
      impact: 'Good user experience, efficient resource usage',
      solution: 'Continue monitoring and maintain current optimization practices',
      estimatedImprovement: 'Performance is already optimized'
    });
  }
  
  return insights;
};

// Get performance score (0-100)
export const calculatePerformanceScore = (metrics: ImageMetrics): number => {
  let score = 100;
  
  // File size penalty
  if (metrics.fileSize > 10 * 1024 * 1024) score -= 30;
  else if (metrics.fileSize > 5 * 1024 * 1024) score -= 15;
  else if (metrics.fileSize > 2 * 1024 * 1024) score -= 5;
  
  // Processing time penalty
  if (metrics.backgroundRemovalTime > 15000) score -= 25;
  else if (metrics.backgroundRemovalTime > 8000) score -= 15;
  else if (metrics.backgroundRemovalTime > 5000) score -= 5;
  
  // Memory usage penalty
  const memoryIncrease = metrics.memoryUsage.peak - metrics.memoryUsage.before;
  if (memoryIncrease > 200) score -= 20;
  else if (memoryIncrease > 100) score -= 10;
  else if (memoryIncrease > 50) score -= 5;
  
  // Total processing time penalty
  if (metrics.totalProcessingTime > 20000) score -= 15;
  else if (metrics.totalProcessingTime > 10000) score -= 8;
  
  // Dimension penalty
  if (metrics.dimensions.width > 4000 || metrics.dimensions.height > 4000) score -= 10;
  
  return Math.max(0, Math.min(100, score));
};
