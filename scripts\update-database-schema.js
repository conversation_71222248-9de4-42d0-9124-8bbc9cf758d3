// Script to update the database schema to fix user registration issues
// Run with: node scripts/update-database-schema.js

const { createClient } = require("@supabase/supabase-js");
const fs = require("fs");
const path = require("path");
require("dotenv").config({ path: ".env.local" });

async function updateDatabaseSchema() {
  console.log("🔧 Updating database schema to fix user registration...\n");

  // Check environment variables
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    console.log("❌ Missing required environment variables:");
    console.log("NEXT_PUBLIC_SUPABASE_URL:", supabaseUrl ? "✅" : "❌");
    console.log("SUPABASE_SERVICE_ROLE_KEY:", supabaseServiceKey ? "✅" : "❌");
    return;
  }

  try {
    // Create Supabase client with service role key for admin operations
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    console.log("📋 Current profiles table structure:");

    // Check current table structure
    const { data: currentColumns, error: columnsError } = await supabase
      .from("information_schema.columns")
      .select("column_name, data_type, is_nullable, column_default")
      .eq("table_name", "profiles")
      .order("ordinal_position");

    if (columnsError) {
      console.log(
        "❌ Error fetching current table structure:",
        columnsError.message
      );
      return;
    }

    console.table(currentColumns);

    // Check if missing columns exist
    const existingColumns = currentColumns.map((col) => col.column_name);
    const requiredColumns = ["images_generated", "paid", "subscription_id"];
    const missingColumns = requiredColumns.filter(
      (col) => !existingColumns.includes(col)
    );

    if (missingColumns.length === 0) {
      console.log("✅ All required columns already exist!");
      return;
    }

    console.log(`\n🔍 Missing columns: ${missingColumns.join(", ")}`);
    console.log("🔧 Adding missing columns...\n");

    // Add missing columns one by one
    for (const column of missingColumns) {
      let sql = "";
      let defaultValue = "";

      switch (column) {
        case "images_generated":
          sql =
            "ALTER TABLE profiles ADD COLUMN images_generated INTEGER DEFAULT 0";
          defaultValue = "0";
          break;
        case "paid":
          sql = "ALTER TABLE profiles ADD COLUMN paid BOOLEAN DEFAULT false";
          defaultValue = "false";
          break;
        case "subscription_id":
          sql =
            "ALTER TABLE profiles ADD COLUMN subscription_id TEXT DEFAULT ''";
          defaultValue = "''";
          break;
      }

      console.log(`Adding column: ${column} (default: ${defaultValue})`);

      // Use direct SQL execution via REST API
      const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec_sql`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${supabaseServiceKey}`,
          apikey: supabaseServiceKey,
        },
        body: JSON.stringify({ sql }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.log(`❌ Error adding column ${column}:`, errorText);
      } else {
        console.log(`✅ Successfully added column: ${column}`);
      }
    }

    // Update existing records to have default values
    console.log("\n🔄 Updating existing records with default values...");

    const { error: updateError } = await supabase
      .from("profiles")
      .update({
        images_generated: 0,
        paid: false,
        subscription_id: "",
      })
      .is("images_generated", null)
      .or("paid.is.null,subscription_id.is.null");

    if (updateError) {
      console.log(
        "⚠️ Note: Could not update existing records:",
        updateError.message
      );
      console.log("This is normal if no existing records need updating.");
    } else {
      console.log("✅ Updated existing records with default values");
    }

    // Verify the final table structure
    console.log("\n📋 Updated profiles table structure:");

    const { data: updatedColumns, error: finalError } = await supabase
      .from("information_schema.columns")
      .select("column_name, data_type, is_nullable, column_default")
      .eq("table_name", "profiles")
      .order("ordinal_position");

    if (finalError) {
      console.log(
        "❌ Error fetching updated table structure:",
        finalError.message
      );
    } else {
      console.table(updatedColumns);
    }

    console.log("\n✅ Database schema update completed!");
    console.log("🎉 User registration should now work properly.");
  } catch (error) {
    console.log("\n❌ Schema update failed:", error.message);
    console.log(
      "\n💡 You may need to run the SQL manually in Supabase dashboard:"
    );
    console.log("1. Go to Supabase Dashboard → SQL Editor");
    console.log("2. Run the SQL from scripts/fix-profiles-table.sql");
  }
}

updateDatabaseSchema();
